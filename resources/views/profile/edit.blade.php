<x-app-layout>
    @section('title', 'Profile Settings')
    @section('description', 'Manage your profile information and account settings')

    <!-- Hero Section -->
    <x-hero-section
        title="Profile Settings"
        subtitle="Manage your account information and <span class='font-semibold text-gradient-primary'>preferences</span>"
        description=""
        :showSteps="false"
    />

    <div class="min-h-screen">
        <div class="py-8">
            <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">

                <!-- Profile Incomplete Message -->
                @if(session('profile_incomplete_message') || session('warning'))
                    <div class="mb-8 animate-slide-up">
                        <div class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl p-6 shadow-lg backdrop-blur-sm">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-14 h-14 bg-gradient-to-r from-amber-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
                                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-amber-900 mb-3">Complete Your Profile to Continue</h3>
                                    <p class="text-amber-700 mb-4 leading-relaxed">
                                        {{ session('profile_incomplete_message') ?? session('warning') }}
                                    </p>
                                    <div class="flex items-center space-x-2 text-sm text-amber-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="font-medium">Your information is secure and private</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="space-y-6">
                    <!-- Clean Dashboard -->
                    <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
                        <div class="p-6">
                            <!-- Clean Grid Layout -->
                            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                                <!-- Profile Completion - Clean -->
                                <div class="bg-white rounded-lg border border-gray-200 p-5 hover:shadow-sm transition-all duration-300">
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                </div>
                                                <span class="text-sm font-medium text-gray-900">Profile</span>
                                            </div>
                                            @if(auth()->user()->isProfileComplete())
                                                <span class="text-xs font-medium text-green-700 bg-green-50 px-2 py-1 rounded-full border border-green-200">Complete</span>
                                            @else
                                                <span class="text-sm font-semibold text-blue-600">{{ auth()->user()->getProfileCompletionPercentage() }}%</span>
                                            @endif
                                        </div>
                                        <div class="w-full bg-gray-100 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
                                                 style="width: {{ auth()->user()->getProfileCompletionPercentage() }}%"></div>
                                        </div>
                                        @if(!auth()->user()->isProfileComplete())
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-50 text-orange-700 border border-orange-200">
                                                {{ 100 - auth()->user()->getProfileCompletionPercentage() }}% remaining
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                <!-- Current Balance -->
                                <div class="bg-white rounded-lg border border-gray-200 p-5 hover:shadow-sm transition-all duration-300">
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                </svg>
                                            </div>
                                            <span class="text-sm font-medium text-gray-700">Balance</span>
                                        </div>
                                        <span class="text-lg font-semibold text-gray-900">₹{{ number_format($currentBalance, 2) }}</span>
                                    </div>
                                </div>

                                <!-- Total Earnings -->
                                <div class="bg-white rounded-lg border border-gray-200 p-5 hover:shadow-sm transition-all duration-300">
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                                                </svg>
                                            </div>
                                            <span class="text-sm font-medium text-gray-700">Earnings</span>
                                        </div>
                                        <span class="text-lg font-semibold text-gray-900">₹{{ number_format($totalEarnings, 2) }}</span>
                                    </div>
                                </div>

                                <!-- Total Spending -->
                                <div class="bg-white rounded-lg border border-gray-200 p-5 hover:shadow-sm transition-all duration-300">
                                    <div class="space-y-3">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                                                </svg>
                                            </div>
                                            <span class="text-sm font-medium text-gray-700">Spending</span>
                                        </div>
                                        <span class="text-lg font-semibold text-gray-900">₹{{ number_format($totalSpending, 2) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Forms Section -->
                    <div class="w-full">
                        <!-- Clean Tabbed Interface -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <!-- Clean Tab Navigation -->
                            <div class="border-b border-gray-200 bg-white rounded-t-xl">
                                <nav class="flex flex-wrap gap-1 sm:gap-2 lg:gap-4 px-6 overflow-x-auto" aria-label="Tabs">
                                    <button onclick="switchTab('personal')" id="personal-tab" class="tab-button active py-3 sm:py-4 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap">
                                        <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <span class="hidden sm:inline">Personal Information</span>
                                        <span class="sm:hidden">Personal</span>
                                    </button>
                                    <!-- Gallery Tab - Show if feature is enabled AND user has enabled it -->
                                    @if(\App\Models\Feature::isEnabled('gallery') && $user->show_gallery_images)
                                        <button onclick="switchTab('gallery')" id="gallery-tab" class="tab-button py-3 sm:py-4 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            Gallery
                                        </button>
                                    @endif
                                    @if(\App\Models\Feature::isEnabled('time_spending') && $user->is_time_spending_enabled)
                                        @php
                                            $isTimeSpendingIncomplete = !$user->hourly_rate || !$user->service_location;
                                            $hasExpiredSubscription = $user->hasExpiredTimeSpendingSubscription();
                                            $hasNoActiveSubscription = !\App\Models\Feature::isSubscriptionModelEnabled() ? false : !$user->hasActiveTimeSpendingSubscription();

                                            // Show red if subscription is expired OR if no active subscription (when subscription model is enabled)
                                            $timeSpendingTabClass = ($hasExpiredSubscription || $hasNoActiveSubscription) ? 'tab-button tab-button-incomplete py-4 px-1 border-b-2 font-medium text-sm' : 'tab-button py-4 px-1 border-b-2 font-medium text-sm';
                                        @endphp
                                        <button onclick="switchTab('time-spending')" id="time-spending-tab" class="{{ str_replace(['py-4 px-1', 'text-sm'], ['py-3 sm:py-4 px-2 sm:px-1', 'text-xs sm:text-sm whitespace-nowrap'], $timeSpendingTabClass) }}">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="hidden sm:inline">Time Spending</span>
                                            <span class="sm:hidden">Time</span>
                                            @if($user->hasActiveTimeSpendingSubscription())
                                                <span class="ml-1 inline-flex items-center px-1 sm:px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <span class="hidden sm:inline">Active</span>
                                                    <span class="sm:hidden">✓</span>
                                                </span>
                                            @elseif($user->hasExpiredTimeSpendingSubscription())
                                                <span class="ml-1 inline-flex items-center px-1 sm:px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    <span class="hidden sm:inline">Expired</span>
                                                    <span class="sm:hidden">✗</span>
                                                </span>
                                            @endif
                                        </button>
                                    @endif
                                    @if(\App\Models\Feature::isEnabled('partner_swapping') && $user->is_couple_activity_enabled)
                                        <button onclick="switchTab('couple-activity')" id="couple-activity-tab" class="tab-button py-3 sm:py-4 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                            </svg>
                                            <span class="hidden sm:inline">Couple Activity</span>
                                            <span class="sm:hidden">Couple</span>
                                        </button>
                                    @endif
                                    @if(\App\Helpers\FeatureHelper::isRatingReviewSystemActive() && $user->is_time_spending_enabled)
                                        <button onclick="switchTab('reviews')" id="reviews-tab" class="tab-button py-3 sm:py-4 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            <span class="hidden sm:inline">Reviews ({{ $user->getTotalReviewsCount() }})</span>
                                            <span class="sm:hidden">Reviews</span>
                                        </button>
                                    @endif
                                    @if(\App\Models\Feature::isEnabled('sugar_partner') && $user->interested_in_sugar_partner)
                                        <button onclick="switchTab('sugar-partner')" id="sugar-partner-tab" class="tab-button py-3 sm:py-4 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="hidden sm:inline">Sugar Partner</span>
                                            <span class="sm:hidden">Sugar</span>
                                        </button>
                                    @endif

                                    <button onclick="switchTab('settings')" id="settings-tab" class="tab-button py-3 sm:py-4 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm whitespace-nowrap">
                                        <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        Settings
                                    </button>
                                </nav>
                            </div>

                            <!-- Clean Tab Content -->
                            <div class="p-6">
                                <!-- Personal Information Tab -->
                                <div id="personal-content" class="tab-content">
                                    @include('profile.partials.update-profile-information-form')
                                </div>

                                <!-- Gallery Tab - Show content if feature is enabled AND user has enabled it -->
                                @if(\App\Models\Feature::isEnabled('gallery') && $user->show_gallery_images)
                                    <div id="gallery-content" class="tab-content hidden">
                                        @include('profile.partials.gallery-form')
                                    </div>
                                @endif

                                <!-- Time Spending Tab -->
                                @if(\App\Models\Feature::isEnabled('time_spending') && $user->is_time_spending_enabled)
                                    <div id="time-spending-content" class="tab-content hidden">
                                        @include('profile.partials.time-spending-form')

                                        <!-- Subscription Plans Section -->
                                        <div class="mt-8" id="subscription-plans-section" style="display: none;">
                                            @include('profile.partials.subscription-display')
                                        </div>
                                    </div>
                                @endif

                                <!-- Couple Activity Tab -->
                                @if(\App\Models\Feature::isEnabled('partner_swapping') && $user->is_couple_activity_enabled)
                                    <div id="couple-activity-content" class="tab-content hidden">
                                        @include('profile.partials.couple-activity-form')
                                    </div>
                                @endif

                                <!-- Sugar Partner Tab -->
                                @if(\App\Models\Feature::isEnabled('sugar_partner') && $user->interested_in_sugar_partner)
                                    <div id="sugar-partner-content" class="tab-content hidden">
                                        @include('profile.partials.sugar-partner-form')
                                    </div>
                                @endif

                                <!-- Reviews Tab -->
                                @if(\App\Helpers\FeatureHelper::isRatingReviewSystemActive() && $user->is_time_spending_enabled)
                                    <div id="reviews-content" class="tab-content hidden">
                                        @include('profile.partials.reviews-display')
                                    </div>
                                @endif

                                <!-- Settings Tab -->
                                <div id="settings-content" class="tab-content hidden">
                                    @include('profile.partials.privacy-settings-form')
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>



    <style>
        /* Enhanced Tab Button Styling */
        .tab-button {
            position: relative;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            color: #6b7280;
            background: transparent;
        }

        .tab-button:hover {
            color: #4f46e5;
            background: rgba(79, 70, 229, 0.05);
            border-bottom-color: rgba(79, 70, 229, 0.3);
        }

        .tab-button.active {
            color: #4f46e5 !important;
            background: rgba(79, 70, 229, 0.1) !important;
            border-bottom-color: #4f46e5 !important;
            font-weight: 600 !important;
            position: relative;
        }

        .tab-button.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);
            border-radius: 0 0 4px 4px;
        }

        /* Incomplete tab styling */
        .tab-button-incomplete {
            color: #dc2626 !important;
            background: rgba(220, 38, 38, 0.05) !important;
            border-bottom-color: #dc2626 !important;
        }

        .tab-button-incomplete.active {
            color: #dc2626 !important;
            background: rgba(220, 38, 38, 0.1) !important;
            border-bottom-color: #dc2626 !important;
        }

        .tab-button-incomplete.active::before {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        }

        /* Tab content transition */
        .tab-content {
            transition: opacity 0.2s ease-in-out;
        }

        .tab-content.hidden {
            display: none !important;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 640px) {
            .tab-button {
                font-size: 0.75rem;
                padding: 0.75rem 0.5rem;
            }

            .tab-button.active {
                font-weight: 700 !important;
            }
        }
    </style>

    <script>
        // Prevent multiple simultaneous tab switches
        let isTabSwitching = false;

        function switchTab(tabName) {
            // Prevent multiple simultaneous calls
            if (isTabSwitching) {
                return;
            }
            isTabSwitching = true;
            // Use requestAnimationFrame to prevent forced reflow
            requestAnimationFrame(() => {
                try {
                    // Prevent infinite recursion
                    if (tabName === 'personal' && (!document.getElementById('personal-content') || !document.getElementById('personal-tab'))) {
                        console.error('Personal tab not found - this should not happen');
                        isTabSwitching = false;
                        return;
                    }

                    // Check if the tab exists (might be hidden due to privacy settings)
                    const tabContent = document.getElementById(tabName + '-content');
                    const tabButton = document.getElementById(tabName + '-tab');

                    if (!tabContent || !tabButton) {
                        // If tab doesn't exist, switch to personal tab as fallback (but avoid infinite recursion)
                        if (tabName !== 'personal') {
                            isTabSwitching = false;
                            switchTab('personal');
                        } else {
                            isTabSwitching = false;
                        }
                        return;
                    }

                    // Batch DOM operations to prevent multiple reflows
                    const allTabContents = document.querySelectorAll('.tab-content');
                    const allTabButtons = document.querySelectorAll('.tab-button');

                    // Hide all tab contents and remove active classes in one pass
                    allTabContents.forEach(content => {
                        if (content !== tabContent) {
                            content.classList.add('hidden');
                        }
                    });

                    allTabButtons.forEach(button => {
                        if (button !== tabButton) {
                            button.classList.remove('active');
                        }
                    });

                    // Show selected tab content and add active class
                    tabContent.classList.remove('hidden');
                    tabButton.classList.add('active');

                    // Store current tab in localStorage for persistence
                    try {
                        localStorage.setItem('profileActiveTab', tabName);
                    } catch (e) {
                        console.warn('Could not save tab to localStorage:', e);
                    }

                    // Update hidden inputs in all forms to track current tab
                    const currentTabInputs = document.querySelectorAll('input[name="current_tab"]');
                    currentTabInputs.forEach(input => {
                        input.value = tabName;
                    });

                    // Reset the flag
                    isTabSwitching = false;
                } catch (error) {
                    console.error('Error in switchTab:', error);
                    isTabSwitching = false;
                }
            });
        }

        // Auto-switch to personal tab if current active tab becomes unavailable
        document.addEventListener('DOMContentLoaded', function() {
            // Use setTimeout to defer execution and prevent blocking
            setTimeout(() => {
                try {
                    // Check for saved tab or URL parameter
                    const urlParams = new URLSearchParams(window.location.search);
                    const tabFromUrl = urlParams.get('tab');
                    let savedTab = null;

                    try {
                        savedTab = localStorage.getItem('profileActiveTab');
                    } catch (e) {
                        console.warn('Could not access localStorage:', e);
                    }

                    let targetTab = 'personal'; // default

                    // Priority: URL parameter > saved tab > default
                    if (tabFromUrl) {
                        targetTab = tabFromUrl;
                    } else if (savedTab) {
                        targetTab = savedTab;
                    }

                    // Check if target tab exists and is available
                    const targetTabButton = document.getElementById(targetTab + '-tab');
                    const targetTabContent = document.getElementById(targetTab + '-content');

                    if (targetTabButton && targetTabContent) {
                        switchTab(targetTab);
                    } else {
                        // Fallback to personal tab if target doesn't exist
                        switchTab('personal');
                    }

                    // Initialize Time Spending tab styling check with delay
                    setTimeout(() => {
                        if (typeof updateTimeSpendingTabStyling === 'function') {
                            updateTimeSpendingTabStyling();
                        }
                    }, 100);

                } catch (error) {
                    console.error('Error initializing profile tabs:', error);
                    // Ensure personal tab is shown as fallback
                    setTimeout(() => {
                        try {
                            switchTab('personal');
                        } catch (fallbackError) {
                            console.error('Critical error: Cannot initialize profile tabs:', fallbackError);
                        }
                    }, 50);
                }
            }, 0);
        });

        // Function to update Time Spending tab styling based on subscription status
        window.updateTimeSpendingTabStyling = function() {
            const timeSpendingTab = document.getElementById('time-spending-tab');
            if (!timeSpendingTab) return;

            // Apply red styling for expired subscriptions OR no active subscription (when subscription model is enabled)
            @if(\App\Models\Feature::isEnabled('time_spending') && $user->is_time_spending_enabled)
                const hasExpiredSubscription = @json($user->hasExpiredTimeSpendingSubscription() ?? false);
                const hasNoActiveSubscription = @json(\App\Models\Feature::isSubscriptionModelEnabled() ? !$user->hasActiveTimeSpendingSubscription() : false);

                if (hasExpiredSubscription || hasNoActiveSubscription) {
                    timeSpendingTab.classList.add('tab-button-incomplete');
                } else {
                    timeSpendingTab.classList.remove('tab-button-incomplete');
                }
            @endif
        }
    </script>
</x-app-layout>
