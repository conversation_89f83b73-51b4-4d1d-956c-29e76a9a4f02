<!-- Compact Sugar Partner Header -->
<div class="text-center mb-4">
    <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-rose-600 to-pink-600 rounded-lg mb-2 shadow-sm">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
    </div>
    <h2 class="text-lg font-bold text-gray-900 mb-1">Sugar Partner</h2>
    <p class="text-gray-600 text-xs max-w-md mx-auto">Configure your preferences for meaningful connections and arrangements</p>
</div>

<!-- Sugar Partner Exchanges Section - Moved to Top -->
@php
    $pendingExchanges = \App\Models\SugarPartnerExchange::where(function($query) use ($user) {
        $query->where('user1_id', $user->id)->orWhere('user2_id', $user->id);
    })->whereIn('status', ['pending_payment', 'payment_completed', 'profiles_viewed'])
    ->with(['user1', 'user2', 'payments'])
    ->orderBy('created_at', 'desc')
    ->get();
@endphp

@if($pendingExchanges->count() > 0)
<div class="bg-white rounded-lg p-4 border border-gray-200 mb-4">
    <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
        <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
        </svg>
        Active Sugar Partner Exchanges
    </h4>

    <div class="space-y-4">
        @foreach($pendingExchanges as $exchange)
            @php
                $otherUser = $exchange->getOtherUser($user->id);
                $userHasPaid = $exchange->userHasPaid($user->id);
                $anyUserPaid = $exchange->anyUserPaid();
                $userPrice = $exchange->getPriceForUser($user->id);

                // Get the Sugar Partner role label based on what the current user wants
                $userWants = $user->getWhatIWant();
                $otherUserType = $otherUser->getWhatIAm();

                // Determine the role label for the other user based on what the current user is seeking
                $roleLabel = 'Your Sugar Partner';
                if (in_array('sugar_daddy', $userWants)) {
                    $roleLabel = 'Your Sugar Daddy';
                } elseif (in_array('sugar_mommy', $userWants)) {
                    $roleLabel = 'Your Sugar Mommy';
                } elseif (in_array('sugar_babe', $userWants)) {
                    $roleLabel = 'Your Sugar Babe';
                } elseif (in_array('sugar_boy', $userWants)) {
                    $roleLabel = 'Your Sugar Boy';
                } else {
                    // Fallback: try to determine from the other user's actual type
                    if ($otherUserType) {
                        $roleLabel = match($otherUserType) {
                            'sugar_daddy' => 'Your Sugar Daddy',
                            'sugar_mommy' => 'Your Sugar Mommy',
                            'sugar_babe' => 'Your Sugar Babe',
                            'sugar_boy' => 'Your Sugar Boy',
                            default => 'Your Sugar Partner'
                        };
                    }
                }

                // Use generic avatar and role label until any user has paid
                $displayName = $anyUserPaid ? $otherUser->name : $roleLabel;
                $displayAvatar = $anyUserPaid ?
                    ($otherUser->profile_picture ? asset('storage/' . $otherUser->profile_picture) : asset('images/default-avatar.png')) :
                    asset('images/default-avatar.png');
            @endphp

            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            @if($anyUserPaid)
                                <img src="{{ $displayAvatar }}" alt="{{ $displayName }}" class="w-10 h-10 rounded-full mr-3">
                            @else
                                <div class="w-10 h-10 rounded-full mr-3 bg-gradient-to-r from-rose-400 to-pink-400 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            @endif
                            <div>
                                <h5 class="font-semibold text-gray-800">{{ $displayName }}</h5>
                                <p class="text-sm text-gray-600">
                                    @if($anyUserPaid)
                                        Exchange initiated {{ $exchange->created_at->diffForHumans() }}
                                    @else
                                        <span class="inline-flex items-center">
                                            <svg class="w-3 h-3 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                            </svg>
                                            Identity protected until payment
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>

                        <div class="mb-3">
                            @if(!$userHasPaid)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    Payment Required: {{ $exchange->currency }} {{ $userPrice }}
                                </span>
                            @elseif(!$anyUserPaid)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Waiting for payment to view profile
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Ready to view profile
                                </span>
                            @endif
                        </div>
                    </div>

                    <div class="ml-4 flex flex-col space-y-2">
                        @if(!$userHasPaid)
                            <!-- Payment Buttons -->
                            <button type="button"
                                    class="px-3 py-1 text-xs font-medium text-white bg-rose-600 hover:bg-rose-700 rounded-md transition-colors payment-btn"
                                    data-exchange-id="{{ $exchange->id }}"
                                    data-amount="{{ $userPrice }}"
                                    data-currency="{{ $exchange->currency }}">
                                Pay {{ $exchange->currency }} {{ $userPrice }}
                            </button>
                        @elseif($anyUserPaid)
                            <!-- View Profile Button -->
                            <button type="button"
                                    class="px-3 py-1 text-xs font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors view-profile-btn"
                                    data-exchange-id="{{ $exchange->id }}"
                                    data-user-id="{{ $otherUser->id }}">
                                View Profile
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
@endif

<div class="bg-white rounded-xl border border-gray-200 shadow-sm">
    <div class="space-y-4 p-4">
        <!-- Photo Upload Validation Error -->
        @if($errors->has('gallery_required'))
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div>
                        <h4 class="text-sm font-semibold text-red-800 mb-1">Photos Required</h4>
                        <p class="text-sm text-red-700">{{ $errors->first('gallery_required') }}</p>
                        <a href="{{ route('profile.edit', ['tab' => 'gallery']) }}" class="inline-flex items-center mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs font-medium rounded-md transition-colors">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Go to Gallery
                        </a>
                    </div>
                </div>
            </div>
        @endif

        <form method="post" action="{{ route('profile.sugar-partner.update') }}" class="space-y-4">
            @csrf
            @method('patch')
            <input type="hidden" name="current_tab" value="sugar-partner">

            <!-- Partner Preferences Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                @php
                    // Get user info first
                    $userAge = $user->getAge();
                    $userGender = $user->gender;

                    $whatIAmOptions = $user->getWhatIAmOptions();
                    $whatIWantOptions = $user->getWhatIWantOptions();
                    $selectedTypes = old('sugar_partner_types', $user->sugar_partner_types ?? []);

                    // Get smart defaults based on age and existing selections
                    $smartDefaults = $user->getSmartDefaults();
                    $currentWhatIAm = old('what_i_am', $smartDefaults['what_i_am']);
                    $currentWhatIWant = old('what_i_want', $smartDefaults['what_i_want']);

                    // Ensure $errors is available (for testing compatibility)
                    if (!isset($errors)) {
                        $errors = new \Illuminate\Support\MessageBag();
                    }
                @endphp

                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Relationship Preferences
                </h4>
                <p class="text-xs text-gray-600 mb-4">
                    Define your role and preferences in sugar partner relationships:
                    @if($userAge)
                        <span class="inline-block ml-2 px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                            Age: {{ $userAge }} years
                            @if($userAge < 30)
                                (Young - Seeking support recommended)
                            @else
                                (Experienced - Providing support recommended)
                            @endif
                        </span>
                    @endif
                </p>

                <!-- Section 1: What I Am (Dropdown) -->
                <div class="mb-6">
                    <h5 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        What I Am
                        <span class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-600 text-xs font-medium rounded-full">Single Selection</span>
                    </h5>
                    <p class="text-xs text-gray-600 mb-3">
                        Select your role as a provider (optional):
                    </p>

                    <select name="what_i_am" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500 text-sm">
                        <option value="">Select your provider role (optional)</option>
                        @foreach($whatIAmOptions as $value => $option)
                            <option value="{{ $value }}" {{ $currentWhatIAm === $value ? 'selected' : '' }}>
                                {{ $option['label'] }}
                            </option>
                        @endforeach
                    </select>
                    <x-input-error class="mt-1" :messages="$errors->get('what_i_am')" />
                </div>

                <!-- Section 2: What I Want (Checkboxes) -->
                <div class="mb-4">
                    <h5 class="text-sm font-semibold text-gray-900 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                        What I Want
                        <span class="ml-2 px-2 py-0.5 bg-pink-100 text-pink-600 text-xs font-medium rounded-full">Multiple Selection</span>
                    </h5>
                    <p class="text-xs text-gray-600 mb-3">
                        Select what you're seeking (you can choose multiple):
                    </p>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3" id="what-i-want-options">
                        @foreach($whatIWantOptions as $value => $option)
                            <label class="relative flex items-start p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-rose-50 hover:border-rose-300 transition-all duration-200 {{ in_array($value, $currentWhatIWant) ? 'bg-rose-50 border-rose-300' : '' }}" data-option-value="{{ $value }}">
                                <input type="checkbox"
                                       name="what_i_want[]"
                                       value="{{ $value }}"
                                       {{ in_array($value, $currentWhatIWant) ? 'checked' : '' }}
                                       class="w-4 h-4 text-rose-600 bg-white border-gray-300 rounded focus:ring-rose-500 focus:ring-2 mt-0.5">
                                <div class="ml-3 flex-1">
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 rounded-full bg-{{ $option['color'] }}-500 mr-2"></div>
                                        <span class="text-sm font-medium text-gray-900">{{ $option['label'] }}</span>
                                    </div>
                                    <p class="text-xs text-gray-600 mt-1">{{ $option['description'] }}</p>
                                </div>
                            </label>
                        @endforeach
                    </div>
                    <x-input-error class="mt-2" :messages="$errors->get('what_i_want')" />
                    <x-input-error class="mt-2" :messages="$errors->get('what_i_want.*')" />
                </div>

                <!-- Hidden field to maintain backward compatibility -->
                <input type="hidden" name="sugar_partner_types_combined" id="sugar_partner_types_combined" value="">

                <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_types')" />
                <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_types.*')" />
            </div>

            <!-- Bio Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Profile Bio
                    <span class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">Optional</span>
                </h4>
                <div>
                    <textarea id="sugar_partner_bio"
                              name="sugar_partner_bio"
                              rows="3"
                              placeholder="Describe what you're looking for in a sugar partner relationship..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500 text-sm">{{ old('sugar_partner_bio', $user->sugar_partner_bio ?? '') }}</textarea>
                    <p class="text-xs text-gray-500 mt-1">This will be displayed on your profile to potential sugar partners.</p>
                    <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_bio')" />
                </div>
            </div>

            <!-- Expectations Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                    Expectations & Boundaries
                    <span class="ml-2 px-2 py-0.5 bg-gray-100 text-gray-600 text-xs font-medium rounded-full">Optional</span>
                </h4>
                <div>
                    <textarea id="sugar_partner_expectations"
                              name="sugar_partner_expectations"
                              rows="3"
                              placeholder="Describe your expectations, boundaries, and what you can offer..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-rose-500 text-sm">{{ old('sugar_partner_expectations', $user->sugar_partner_expectations ?? '') }}</textarea>
                    <p class="text-xs text-gray-500 mt-1">Be clear about your expectations to find compatible matches.</p>
                    <x-input-error class="mt-2" :messages="$errors->get('sugar_partner_expectations')" />
                </div>
            </div>

            

            <!-- Save Button -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <div class="flex items-center justify-end">
                    <button type="submit" class="px-4 py-2 bg-rose-600 text-white font-medium rounded-lg hover:bg-rose-700 focus:outline-none focus:ring-2 focus:ring-rose-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-sm">
                        Save Sugar Partner Settings
                    </button>
                </div>
            </div>

            <!-- Success Messages -->
            @if (session('status') === 'sugar-partner-updated')
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <p class="text-xs text-green-800">Sugar partner settings updated successfully!</p>
                    </div>
                </div>
            @endif

            @if (session('success'))
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <p class="text-xs text-green-800">{{ session('success') }}</p>
                    </div>
                </div>
            @endif

            @if (session('error'))
                <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        <p class="text-xs text-red-800">{{ session('error') }}</p>
                    </div>
                </div>
            @endif
        </div>
    </form>
</div>



<style>
/* Sugar Partner specific styles */
.sugar-partner-option {
    transition: all 0.2s ease;
}

.sugar-partner-option:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(244, 63, 94, 0.15);
}

.sugar-partner-option input[type="checkbox"]:checked + div {
    color: #e11d48;
}

.sugar-partner-option input[type="checkbox"]:checked {
    background-color: #e11d48;
    border-color: #e11d48;
}

/* Enhanced focus states */
textarea:focus {
    box-shadow: 0 0 0 3px rgba(244, 63, 94, 0.1);
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    .sugar-partner-grid {
        grid-template-columns: 1fr;
    }

    .sugar-partner-option {
        padding: 0.75rem;
    }
}

/* Animation for success message */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-message {
    animation: slideIn 0.3s ease-out;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add enhanced styling classes for checkboxes
    const options = document.querySelectorAll('label[class*="relative flex items-start"]');
    options.forEach(option => {
        option.classList.add('sugar-partner-option');
    });

    // Add grid class for responsive design
    const grid = document.querySelector('.grid.grid-cols-1.sm\\:grid-cols-2');
    if (grid) {
        grid.classList.add('sugar-partner-grid');
    }

    // Add success message animation
    const successMessage = document.querySelector('.bg-green-50');
    if (successMessage) {
        successMessage.classList.add('success-message');
    }

    // Enhanced checkbox interaction with rose color scheme for "What I Want" section
    const whatIWantCheckboxes = document.querySelectorAll('input[name="what_i_want[]"]');
    whatIWantCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const label = this.closest('label');
            if (this.checked) {
                label.style.borderColor = '#e11d48';
                label.style.backgroundColor = '#fdf2f8';
            } else {
                label.style.borderColor = '#e5e7eb';
                label.style.backgroundColor = '';
            }
            updateCombinedField();
        });

        // Set initial state
        if (checkbox.checked) {
            const label = checkbox.closest('label');
            label.style.borderColor = '#e11d48';
            label.style.backgroundColor = '#fdf2f8';
        }
    });

    // Handle dropdown change for "What I Am" section
    const whatIAmSelect = document.querySelector('select[name="what_i_am"]');
    if (whatIAmSelect) {
        whatIAmSelect.addEventListener('change', function() {
            updateCombinedField();
            updateOptionVisibility();
        });
    }

    // Add change listeners to checkboxes for dynamic filtering
    whatIWantCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateOptionVisibility();
        });
    });

    // Initialize filtering on page load
    updateOptionVisibility();

    // Function to update option visibility based on selections
    function updateOptionVisibility() {
        const whatIAmValue = whatIAmSelect ? whatIAmSelect.value : '';

        // Filter "What I Want" options to exclude the selected "What I Am" option
        const whatIWantContainer = document.getElementById('what-i-want-options');
        if (whatIWantContainer) {
            const optionLabels = whatIWantContainer.querySelectorAll('label[data-option-value]');

            optionLabels.forEach(label => {
                const optionValue = label.getAttribute('data-option-value');
                const checkbox = label.querySelector('input[type="checkbox"]');

                if (optionValue === whatIAmValue) {
                    // Hide the option that matches "What I Am" selection
                    label.style.display = 'none';
                    // Uncheck if it was previously checked
                    if (checkbox && checkbox.checked) {
                        checkbox.checked = false;
                    }
                } else {
                    // Show the option
                    label.style.display = 'flex';
                }
            });
        }

        // Update the combined field for form submission
        updateCombinedField();
    }

    // Function to highlight recommended options
    function highlightRecommendedOptions(recommendedTypes) {
        // Clear previous recommendations
        clearRecommendations();

        // Highlight recommended "What I Am" options
        if (whatIAmSelect) {
            const options = whatIAmSelect.querySelectorAll('option');
            options.forEach(option => {
                if (recommendedTypes.includes(option.value)) {
                    option.style.backgroundColor = '#dbeafe';
                    option.style.fontWeight = 'bold';
                }
            });
        }

        // Highlight recommended "What I Want" options
        whatIWantCheckboxes.forEach(checkbox => {
            if (recommendedTypes.includes(checkbox.value)) {
                const label = checkbox.closest('label');
                if (label && !checkbox.checked) {
                    label.style.boxShadow = '0 0 0 2px #3b82f6';
                    label.style.borderColor = '#3b82f6';
                }
            }
        });
    }

    // Function to clear recommendations
    function clearRecommendations() {
        // Clear dropdown highlights
        if (whatIAmSelect) {
            const options = whatIAmSelect.querySelectorAll('option');
            options.forEach(option => {
                option.style.backgroundColor = '';
                option.style.fontWeight = '';
            });
        }

        // Clear checkbox highlights
        whatIWantCheckboxes.forEach(checkbox => {
            const label = checkbox.closest('label');
            if (label && !checkbox.checked) {
                label.style.boxShadow = '';
                label.style.borderColor = '#e5e7eb';
            }
        });
    }







    // Function to combine values for backward compatibility
    function updateCombinedField() {
        const combinedValues = [];

        // Add "What I Am" selection
        const whatIAmValue = whatIAmSelect ? whatIAmSelect.value : '';
        if (whatIAmValue) {
            combinedValues.push(whatIAmValue);
        }

        // Add "What I Want" selections
        whatIWantCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                combinedValues.push(checkbox.value);
            }
        });

        // Update hidden field
        const combinedField = document.getElementById('sugar_partner_types_combined');
        if (combinedField) {
            combinedField.value = JSON.stringify(combinedValues);
        }
    }

    // Form submission handler to ensure backward compatibility
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            updateCombinedField();

            // Create hidden inputs for the original field name
            const existingHiddenInputs = form.querySelectorAll('input[name="sugar_partner_types[]"]');
            existingHiddenInputs.forEach(input => input.remove());

            const combinedField = document.getElementById('sugar_partner_types_combined');
            if (combinedField && combinedField.value) {
                const values = JSON.parse(combinedField.value);
                values.forEach(value => {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'sugar_partner_types[]';
                    hiddenInput.value = value;
                    form.appendChild(hiddenInput);
                });
            }
        });
    }

    // Initialize combined field on page load
    updateCombinedField();

    // Character counter for textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        const maxLength = 500; // Set reasonable limit

        // Create character counter
        const counter = document.createElement('div');
        counter.className = 'text-xs text-gray-400 mt-1 text-right';
        counter.textContent = `${textarea.value.length}/${maxLength}`;

        // Insert after the existing help text
        const helpText = textarea.parentNode.querySelector('.text-xs.text-gray-500');
        if (helpText) {
            helpText.parentNode.insertBefore(counter, helpText.nextSibling);
        } else {
            textarea.parentNode.appendChild(counter);
        }

        // Update counter on input
        textarea.addEventListener('input', function() {
            const length = this.value.length;
            counter.textContent = `${length}/${maxLength}`;

            if (length > maxLength * 0.9) {
                counter.style.color = '#ef4444';
            } else if (length > maxLength * 0.8) {
                counter.style.color = '#f59e0b';
            } else {
                counter.style.color = '#9ca3af';
            }
        });
    });
    // Add event listeners for payment and view profile buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('payment-btn')) {
            e.preventDefault();
            const exchangeId = e.target.getAttribute('data-exchange-id');
            const amount = e.target.getAttribute('data-amount');
            const currency = e.target.getAttribute('data-currency');

            console.log('Payment button clicked:', { exchangeId, amount, currency });
            processAutomaticPayment(exchangeId, amount, currency);
        }

        if (e.target.classList.contains('view-profile-btn')) {
            e.preventDefault();
            const exchangeId = e.target.getAttribute('data-exchange-id');
            const userId = e.target.getAttribute('data-user-id');

            console.log('View Profile button clicked:', { exchangeId, userId });
            viewProfile(exchangeId, userId);
        }
    });
});

// Test function to verify JavaScript is working
function testFunction() {
    console.log('Test function called!');
    alert('JavaScript is working!');
}

// Sugar Partner Exchange Functions - Global scope
function processAutomaticPayment(exchangeId, amount, currency) {
    console.log('=== PAYMENT FUNCTION CALLED ===');
    console.log('Starting automatic payment:', { exchangeId, amount, currency });

    // Show loading indicator
    showToast('Processing payment...', 'info');

    // Disable the payment button to prevent double clicks
    const paymentButton = event.target;
    const originalText = paymentButton.textContent;
    paymentButton.disabled = true;
    paymentButton.textContent = 'Processing...';
    paymentButton.classList.add('opacity-50', 'cursor-not-allowed');

    // Proceed with automatic payment logic
    console.log('Starting automatic payment processing...');
    checkWalletAndProcessPayment(exchangeId, amount, currency, paymentButton, originalText);
}

function checkWalletAndProcessPayment(exchangeId, amount, currency, button, originalText) {
    console.log('Checking wallet balance for automatic payment...');

    // First, get user's wallet balance
    fetch('/wallet/balance', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => {
        console.log('Wallet balance response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(walletData => {
        console.log('Wallet data received:', walletData);
        const walletBalance = walletData.balance || 0;
        const requiredAmount = parseFloat(amount);

        console.log('Payment decision:', { walletBalance, requiredAmount });

        if (walletBalance >= requiredAmount) {
            // Full payment from wallet
            console.log('Processing full wallet payment');
            showToast(`Using wallet balance: ${currency} ${walletBalance} available`, 'info');
            processWalletPayment(exchangeId, requiredAmount, currency, button, originalText);
        } else if (walletBalance > 0) {
            // Partial wallet + Razorpay
            console.log('Processing hybrid payment');
            const razorpayAmount = requiredAmount - walletBalance;
            showToast(`Using ${currency} ${walletBalance} from wallet + ${currency} ${razorpayAmount} online`, 'info');
            processHybridPayment(exchangeId, walletBalance, razorpayAmount, currency, button, originalText);
        } else {
            // Full Razorpay payment
            console.log('Processing full Razorpay payment');
            showToast(`Wallet empty. Processing ${currency} ${requiredAmount} online payment`, 'info');
            processRazorpayPayment(exchangeId, requiredAmount, currency, button, originalText);
        }
    })
    .catch(error => {
        console.error('Error fetching wallet balance:', error);
        showToast('Error checking wallet balance. Processing with online payment...', 'error');
        // Fallback to Razorpay if wallet check fails
        setTimeout(() => {
            processRazorpayPayment(exchangeId, amount, currency, button, originalText);
        }, 1000);
    });
}

function processWalletPayment(exchangeId, amount, currency, button, originalText) {
    console.log('Processing wallet payment:', { exchangeId, amount, currency });

    const url = `/sugar-partner/exchange/${exchangeId}/payment`;
    console.log('Payment URL:', url);

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            payment_method: 'wallet',
            amount: amount
        })
    })
    .then(response => {
        console.log('Wallet payment response status:', response.status);
        console.log('Response headers:', response.headers.get('content-type'));

        // First get the response text to see what we're actually receiving
        return response.text().then(text => {
            console.log('Raw response text:', text);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}, response: ${text}`);
            }

            // Try to parse as JSON
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Failed to parse JSON:', e);
                throw new Error(`Invalid JSON response: ${text.substring(0, 200)}...`);
            }
        });
    })
    .then(data => {
        console.log('Wallet payment response:', data);
        if (data.success) {
            showToast(`Payment completed! ${currency} ${amount} deducted from wallet.`, 'success');
            // Reload the page to show updated status
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showToast(data.message || 'Wallet payment failed', 'error');
            resetPaymentButton(button, originalText);
        }
    })
    .catch(error => {
        console.error('Wallet payment error:', error);
        showToast(`Payment failed: ${error.message}`, 'error');
        resetPaymentButton(button, originalText);
    });
}

function processHybridPayment(exchangeId, walletAmount, razorpayAmount, currency, button, originalText) {
    // First deduct from wallet
    fetch(`/sugar-partner/exchange/${exchangeId}/payment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            payment_method: 'wallet',
            amount: walletAmount,
            is_partial: true
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`${currency} ${walletAmount} deducted from wallet. Processing remaining ${currency} ${razorpayAmount} online...`, 'info');
            // Now process remaining amount with Razorpay
            setTimeout(() => {
                processRazorpayPayment(exchangeId, razorpayAmount, currency, button, originalText, walletAmount);
            }, 1000);
        } else {
            showToast(data.message || 'Wallet deduction failed', 'error');
            resetPaymentButton(button, originalText);
        }
    })
    .catch(error => {
        showToast('Wallet deduction failed. Processing full amount online...', 'error');
        // Fallback to full Razorpay payment
        setTimeout(() => {
            processRazorpayPayment(exchangeId, walletAmount + razorpayAmount, currency, button, originalText);
        }, 1000);
    });
}

function processHybridPayment(exchangeId, walletAmount, razorpayAmount, currency, button, originalText) {
    console.log('Processing hybrid payment:', { exchangeId, walletAmount, razorpayAmount, currency });

    // First deduct from wallet
    fetch(`/sugar-partner/exchange/${exchangeId}/payment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            payment_method: 'wallet',
            amount: walletAmount,
            is_partial: true
        })
    })
    .then(response => {
        console.log('Wallet deduction response status:', response.status);
        return response.text().then(text => {
            console.log('Wallet deduction raw response:', text);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}, response: ${text}`);
            }

            try {
                return JSON.parse(text);
            } catch (e) {
                throw new Error(`Invalid JSON response: ${text.substring(0, 200)}...`);
            }
        });
    })
    .then(data => {
        console.log('Wallet deduction response:', data);
        if (data.success) {
            showToast(`${currency} ${walletAmount} deducted from wallet. Processing remaining ${currency} ${razorpayAmount} online...`, 'info');
            // Now process remaining amount with Razorpay
            setTimeout(() => {
                processRazorpayPayment(exchangeId, razorpayAmount, currency, button, originalText, walletAmount);
            }, 1000);
        } else {
            showToast(data.message || 'Wallet deduction failed', 'error');
            resetPaymentButton(button, originalText);
        }
    })
    .catch(error => {
        console.error('Wallet deduction error:', error);
        showToast('Wallet deduction failed. Processing full amount online...', 'error');
        // Fallback to full Razorpay payment
        setTimeout(() => {
            processRazorpayPayment(exchangeId, walletAmount + razorpayAmount, currency, button, originalText);
        }, 1000);
    });
}

function processRazorpayPayment(exchangeId, amount, currency, button, originalText, walletAmountUsed = 0) {
    console.log('Processing Razorpay payment:', { exchangeId, amount, currency, walletAmountUsed });

    // For demo purposes, we'll simulate a successful payment
    // In production, you would integrate with actual Razorpay API

    fetch(`/sugar-partner/exchange/${exchangeId}/payment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            payment_method: 'razorpay',
            amount: amount,
            wallet_amount_used: walletAmountUsed,
            // Simulate successful Razorpay payment for demo
            razorpay_payment_id: 'demo_pay_' + Date.now(),
            razorpay_order_id: 'demo_order_' + Date.now(),
            razorpay_signature: 'demo_signature_' + Date.now()
        })
    })
    .then(response => {
        console.log('Razorpay payment response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Razorpay payment response:', data);
        if (data.success) {
            let successMessage = `Payment completed successfully!`;
            if (walletAmountUsed > 0) {
                successMessage = `Payment completed! ${currency} ${walletAmountUsed} from wallet + ${currency} ${amount} online.`;
            } else {
                successMessage = `Payment completed! ${currency} ${amount} paid online.`;
            }
            showToast(successMessage, 'success');
            // Reload the page to show updated status
            setTimeout(() => window.location.reload(), 1500);
        } else {
            showToast(data.message || 'Online payment failed', 'error');
            resetPaymentButton(button, originalText);
        }
    })
    .catch(error => {
        console.error('Razorpay payment error:', error);
        showToast('Online payment failed. Please try again.', 'error');
        resetPaymentButton(button, originalText);
    });
}

function resetPaymentButton(button, originalText) {
    button.disabled = false;
    button.textContent = originalText;
    button.classList.remove('opacity-50', 'cursor-not-allowed');
}

function viewProfile(exchangeId, userId) {
    console.log('=== VIEW PROFILE FUNCTION CALLED ===');
    console.log('Parameters:', { exchangeId, userId });

    // Create profile modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modal.innerHTML = `
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-lg">
            <!-- Modal Header -->
            <div class="bg-gray-700 px-4 py-3 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-white">Sugar Partner Profile</h3>
                <button onclick="closeProfileModal()" class="text-white hover:text-gray-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="overflow-y-auto max-h-[calc(90vh-120px)]">
                <div id="profile-content" class="p-4">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-2 border-pink-200 border-t-pink-500 mr-3"></div>
                        <span class="text-gray-600">Loading profile...</span>
                    </div>
                </div>

                <!-- Response Actions -->
                <div class="border-t bg-gray-50 p-4">
                    <div class="text-center mb-4">
                        <h4 class="text-lg font-semibold text-gray-900">Choose Your Response</h4>
                        <p class="text-sm text-gray-600">Select how you'd like to respond to this match</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 max-w-2xl mx-auto">
                        <button onclick="submitResponse(${exchangeId}, 'accept')"
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                            ✓ Accept
                        </button>

                        <button onclick="submitResponse(${exchangeId}, 'soft_reject')"
                                class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                            ⏰ Maybe Later
                        </button>

                        <button onclick="submitResponse(${exchangeId}, 'hard_reject')"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                            ✗ Not Interested
                        </button>
                    </div>

                    <div class="text-center mt-3">
                        <p class="text-xs text-gray-500">Notifications sent when both users have paid</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    window.currentProfileModal = modal;

    // Add smooth entrance animation
    modal.style.opacity = '0';
    modal.style.transition = 'opacity 0.3s ease';
    setTimeout(() => {
        modal.style.opacity = '1';
    }, 10);

    // Load profile content
    const profileUrl = `/sugar-partner/exchange/${exchangeId}/profile/${userId}`;
    console.log('Fetching profile from URL:', profileUrl);

    fetch(profileUrl, {
        headers: {
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
        .then(response => {
            console.log('Profile response status:', response.status);
            console.log('Profile response headers:', response.headers.get('content-type'));

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.text().then(text => {
                console.log('Profile raw response:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    throw new Error(`Invalid JSON response: ${text.substring(0, 200)}...`);
                }
            });
        })
        .then(data => {
            console.log('Profile data received:', data);
            if (data.success) {
                const profile = data.profile;
                const profileHtml = `
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        <!-- Left Column: Profile Picture & Basic Info -->
                        <div class="lg:col-span-1">
                            <div class="bg-white rounded-lg shadow border overflow-hidden">
                                <img src="${profile.profile_picture}" alt="${profile.name}" class="w-full h-64 object-cover">
                                <div class="p-4">
                                    <h4 class="text-xl font-bold text-gray-900">${profile.name}</h4>
                                    <p class="text-gray-600">${profile.age} years old • ${profile.gender}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Detailed Information -->
                        <div class="lg:col-span-2 space-y-5">
                            ${profile.interests ? `
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5 hover:shadow-md transition-shadow">
                                    <div class="flex items-center mb-3">
                                        <div class="w-2 h-6 bg-blue-500 rounded-full mr-3"></div>
                                        <h5 class="text-lg font-semibold text-gray-900">Interests</h5>
                                    </div>
                                    <p class="text-gray-700 leading-relaxed pl-5">${profile.interests}</p>
                                </div>
                            ` : ''}

                            ${profile.expectation ? `
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5 hover:shadow-md transition-shadow">
                                    <div class="flex items-center mb-3">
                                        <div class="w-2 h-6 bg-green-500 rounded-full mr-3"></div>
                                        <h5 class="text-lg font-semibold text-gray-900">General Expectations</h5>
                                    </div>
                                    <p class="text-gray-700 leading-relaxed pl-5">${profile.expectation}</p>
                                </div>
                            ` : ''}

                            ${profile.sugar_partner_bio ? `
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5 hover:shadow-md transition-shadow">
                                    <div class="flex items-center mb-3">
                                        <div class="w-2 h-6 bg-purple-500 rounded-full mr-3"></div>
                                        <h5 class="text-lg font-semibold text-gray-900">Sugar Partner Bio</h5>
                                    </div>
                                    <p class="text-gray-700 leading-relaxed pl-5">${profile.sugar_partner_bio}</p>
                                </div>
                            ` : ''}

                            ${profile.sugar_partner_expectations ? `
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5 hover:shadow-md transition-shadow">
                                    <div class="flex items-center mb-3">
                                        <div class="w-2 h-6 bg-orange-500 rounded-full mr-3"></div>
                                        <h5 class="text-lg font-semibold text-gray-900">Sugar Partner Expectations</h5>
                                    </div>
                                    <p class="text-gray-700 leading-relaxed pl-5">${profile.sugar_partner_expectations}</p>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    ${profile.gallery_images && profile.gallery_images.length > 0 ? `
                        <!-- Gallery Section -->
                        <div class="mt-4">
                            <div class="bg-white rounded-lg shadow border p-4">
                                <h5 class="font-semibold text-gray-900 mb-3">Photos (${profile.gallery_images.length})</h5>
                                <div class="grid grid-cols-3 sm:grid-cols-4 gap-2" id="gallery-grid">
                                    ${profile.gallery_images.map((img, index) => `
                                        <div class="cursor-pointer" onclick="openImageLightbox(${index})">
                                            <img src="${img}" alt="Gallery image ${index + 1}"
                                                 class="w-full h-20 object-cover rounded hover:opacity-75 transition-opacity"
                                                 onerror="this.src='/images/default-avatar.png'; this.onerror=null;">
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    ` : ''}
                `;
                document.getElementById('profile-content').innerHTML = profileHtml;

                // Store gallery images globally for lightbox functionality
                if (profile.gallery_images && profile.gallery_images.length > 0) {
                    currentGalleryImages = profile.gallery_images;
                    console.log('Gallery images loaded:', currentGalleryImages);
                    console.log('Number of gallery images:', currentGalleryImages.length);
                } else {
                    console.log('No gallery images found in profile');
                    currentGalleryImages = [];
                }
            } else {
                console.error('Profile loading failed:', data);
                document.getElementById('profile-content').innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-600 mb-2">
                            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Profile Loading Failed</h3>
                        <p class="text-gray-600">${data.message || 'Unable to load profile. Please try again.'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Profile loading error:', error);
            document.getElementById('profile-content').innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-600 mb-2">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Connection Error</h3>
                    <p class="text-gray-600">Unable to connect to server. Please check your connection and try again.</p>
                    <p class="text-sm text-gray-500 mt-2">Error: ${error.message}</p>
                </div>
            `;
        });
}

function closeProfileModal() {
    if (window.currentProfileModal) {
        // Add smooth exit animation
        window.currentProfileModal.style.opacity = '0';
        setTimeout(() => {
            if (window.currentProfileModal) {
                window.currentProfileModal.remove();
                window.currentProfileModal = null;
            }
        }, 300);
    }
}

function submitResponse(exchangeId, responseType) {
    // Simple confirmation
    const messages = {
        'accept': 'Accept this match?',
        'soft_reject': 'Maybe later for this match?',
        'hard_reject': 'Not interested in this match?'
    };

    if (confirm(messages[responseType])) {
        processResponse(exchangeId, responseType);
    }
}

function processResponse(exchangeId, responseType) {
    fetch(`/sugar-partner/exchange/${exchangeId}/response`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            rejection_type: responseType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            closeProfileModal();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showToast(data.message || 'Failed to submit response', 'error');
        }
    })
    .catch(error => {
        showToast('Failed to submit response. Please try again.', 'error');
    });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded text-white text-sm max-w-sm ${
        type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
        'bg-blue-500'
    }`;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}



// Global variable to store gallery images for lightbox
let currentGalleryImages = [];
let currentImageIndex = 0;

// Function to open image lightbox
function openImageLightbox(index) {
    console.log('Opening lightbox for image index:', index);
    console.log('Current gallery images:', currentGalleryImages);

    // Ensure we have gallery images
    if (!currentGalleryImages || currentGalleryImages.length === 0) {
        console.error('No gallery images available');
        showToast('No images to display', 'error');
        return;
    }

    // Validate index
    if (index < 0 || index >= currentGalleryImages.length) {
        console.error('Invalid image index:', index);
        showToast('Invalid image index', 'error');
        return;
    }

    currentImageIndex = index;
    console.log('Setting current image index to:', currentImageIndex);
    console.log('Image URL:', currentGalleryImages[currentImageIndex]);
    showSugarPartnerLightbox();
}

// Function to show Sugar Partner lightbox modal
function showSugarPartnerLightbox() {
    console.log('Showing Sugar Partner lightbox for image:', currentGalleryImages[currentImageIndex]);

    // Remove any existing lightbox and cleanup
    closeSugarPartnerLightbox();

    // Wait a moment to ensure cleanup is complete
    setTimeout(() => {
        const lightbox = document.createElement('div');
        lightbox.id = 'image-lightbox';
        lightbox.className = 'fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center';
        lightbox.style.zIndex = '9999';
        lightbox.style.position = 'fixed';
        lightbox.style.top = '0';
        lightbox.style.left = '0';
        lightbox.style.width = '100%';
        lightbox.style.height = '100%';

        const imageUrl = currentGalleryImages[currentImageIndex];
        console.log('Creating lightbox with image URL:', imageUrl);

        lightbox.innerHTML = `
            <div class="relative w-full h-full flex items-center justify-center p-4">
                <!-- Close Button -->
                <button onclick="console.log('Close button clicked'); closeSugarPartnerLightbox();"
                        class="absolute top-4 right-4 text-white hover:text-red-400 transition-colors bg-red-600 hover:bg-red-700 rounded-full p-3 border-2 border-white shadow-lg"
                        style="z-index: 10001; cursor: pointer;">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>

                <!-- Navigation Arrows -->
                ${currentGalleryImages.length > 1 ? `
                    <button onclick="console.log('Previous button clicked'); previousSugarPartnerImage();"
                            class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-blue-400 transition-colors bg-blue-600 hover:bg-blue-700 rounded-full p-3 border-2 border-white shadow-lg"
                            style="z-index: 10001; cursor: pointer;">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button onclick="console.log('Next button clicked'); nextSugarPartnerImage();"
                            class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-blue-400 transition-colors bg-blue-600 hover:bg-blue-700 rounded-full p-3 border-2 border-white shadow-lg"
                            style="z-index: 10001; cursor: pointer;">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="3">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                ` : ''}

                <!-- Image Container -->
                <div class="flex items-center justify-center w-full h-full" onclick="closeSugarPartnerLightbox()" style="z-index: 10000;">
                    <img id="lightbox-image"
                         src="${imageUrl}"
                         alt="Gallery image ${currentImageIndex + 1}"
                         class="max-w-full max-h-full object-contain cursor-zoom-in"
                         style="max-height: 90vh; max-width: 90vw;"
                         onerror="console.error('Image failed to load:', this.src); this.src='/images/default-avatar.png'; this.onerror=null;"
                         onload="console.log('Image loaded successfully:', this.src);"
                         onclick="event.stopPropagation(); toggleSugarPartnerImageZoom(this)">
                </div>

                <!-- Image Counter -->
                ${currentGalleryImages.length > 1 ? `
                    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded-full text-sm border border-white border-opacity-30"
                         style="z-index: 10001;">
                        <span id="image-counter">${currentImageIndex + 1} of ${currentGalleryImages.length}</span>
                    </div>
                ` : ''}
            </div>
        `;

        document.body.appendChild(lightbox);
        console.log('Sugar Partner lightbox added to DOM');

        // Add click event listener to close on background click
        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                console.log('Background clicked - closing lightbox');
                closeSugarPartnerLightbox();
            }
        });

        // Add smooth entrance animation
        lightbox.style.opacity = '0';
        lightbox.style.transition = 'opacity 0.3s ease';
        setTimeout(() => {
            lightbox.style.opacity = '1';
            console.log('Sugar Partner lightbox animation complete');
        }, 10);

        // Add keyboard navigation (remove any existing listeners first)
        document.removeEventListener('keydown', handleSugarPartnerLightboxKeydown);
        document.addEventListener('keydown', handleSugarPartnerLightboxKeydown);
        console.log('Sugar Partner lightbox keyboard event listener added');

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
        document.body.style.overflowX = 'hidden';
        document.body.style.overflowY = 'hidden';

    }, 50); // Small delay to ensure cleanup is complete
}

// Function to close Sugar Partner lightbox (renamed to avoid conflicts)
function closeSugarPartnerLightbox() {
    console.log('closeSugarPartnerLightbox function called');
    const lightbox = document.getElementById('image-lightbox');

    // Immediately restore body scroll to prevent scroll bar issues
    document.body.style.overflow = '';
    document.body.style.overflowX = '';
    document.body.style.overflowY = '';
    document.body.classList.remove('overflow-hidden');

    // Remove event listeners immediately
    document.removeEventListener('keydown', handleSugarPartnerLightboxKeydown);

    if (lightbox) {
        console.log('Sugar Partner lightbox found, removing...');

        // Remove lightbox immediately without animation to prevent issues
        lightbox.remove();
        console.log('Sugar Partner lightbox removed from DOM immediately');
    } else {
        console.log('Sugar Partner lightbox not found in DOM');
    }

    // Force cleanup as additional safety measure
    setTimeout(() => {
        forceSugarPartnerLightboxCleanup();
    }, 50);

    console.log('Sugar Partner lightbox closed successfully');
}

// Global cleanup function for Sugar Partner lightbox (renamed to avoid conflicts)
function forceSugarPartnerLightboxCleanup() {
    console.log('Force Sugar Partner lightbox cleanup called');

    // Remove Sugar Partner lightbox elements specifically
    const lightboxes = document.querySelectorAll('#image-lightbox');
    lightboxes.forEach(lightbox => {
        if (lightbox) {
            lightbox.remove();
            console.log('Removed Sugar Partner lightbox element:', lightbox.id);
        }
    });

    // Restore body scroll completely
    document.body.style.overflow = '';
    document.body.style.overflowX = '';
    document.body.style.overflowY = '';
    document.body.classList.remove('overflow-hidden');

    // Reset any potential CSS that might be causing scroll issues
    document.documentElement.style.overflow = '';
    document.documentElement.style.overflowX = '';
    document.documentElement.style.overflowY = '';

    // Remove Sugar Partner keyboard event listeners
    document.removeEventListener('keydown', handleSugarPartnerLightboxKeydown);

    console.log('Force Sugar Partner lightbox cleanup completed');
}

// Add cleanup on page unload
window.addEventListener('beforeunload', function() {
    forceSugarPartnerLightboxCleanup();
});

// Add cleanup on page visibility change
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        const lightbox = document.getElementById('image-lightbox');
        if (lightbox) {
            closeSugarPartnerLightbox();
        }
    }
});

// Function to show previous Sugar Partner image
function previousSugarPartnerImage() {
    console.log('Previous Sugar Partner image called, current index:', currentImageIndex);
    if (!currentGalleryImages || currentGalleryImages.length === 0) {
        console.error('No gallery images available for navigation');
        return;
    }

    if (currentImageIndex > 0) {
        currentImageIndex--;
    } else {
        currentImageIndex = currentGalleryImages.length - 1; // Loop to last image
    }
    console.log('New index:', currentImageIndex);
    updateSugarPartnerLightboxImage();
}

// Function to show next Sugar Partner image
function nextSugarPartnerImage() {
    console.log('Next Sugar Partner image called, current index:', currentImageIndex);
    if (!currentGalleryImages || currentGalleryImages.length === 0) {
        console.error('No gallery images available for navigation');
        return;
    }

    if (currentImageIndex < currentGalleryImages.length - 1) {
        currentImageIndex++;
    } else {
        currentImageIndex = 0; // Loop to first image
    }
    console.log('New index:', currentImageIndex);
    updateSugarPartnerLightboxImage();
}

// Function to update Sugar Partner lightbox image
function updateSugarPartnerLightboxImage() {
    console.log('Updating Sugar Partner lightbox image to index:', currentImageIndex);
    const imageContainer = document.querySelector('#image-lightbox .flex.items-center.justify-center.w-full.h-full');
    const imageCounter = document.getElementById('image-counter');

    if (imageContainer && currentGalleryImages && currentGalleryImages[currentImageIndex]) {
        const newImageUrl = currentGalleryImages[currentImageIndex];
        console.log('Setting image source to:', newImageUrl);

        // Remove old image and create new one
        const oldImage = document.getElementById('lightbox-image');
        if (oldImage) {
            oldImage.remove();
        }

        // Create new image element
        const newImage = document.createElement('img');
        newImage.id = 'lightbox-image';
        newImage.src = newImageUrl;
        newImage.alt = `Gallery image ${currentImageIndex + 1}`;
        newImage.className = 'max-w-full max-h-full object-contain cursor-zoom-in';
        newImage.style.maxHeight = '90vh';
        newImage.style.maxWidth = '90vw';

        newImage.onerror = function() {
            console.error('Image failed to load:', this.src);
            this.src = '/images/default-avatar.png';
            this.onerror = null;
        };

        newImage.onload = function() {
            console.log('Image successfully loaded:', this.src);
        };

        newImage.onclick = function(event) {
            event.stopPropagation();
            toggleSugarPartnerImageZoom(this);
        };

        // Add new image to container
        imageContainer.appendChild(newImage);

        console.log('New image element created and added');
    } else {
        console.error('Could not update lightbox image - container or image not found');
        console.log('imageContainer:', imageContainer);
        console.log('currentGalleryImages:', currentGalleryImages);
        console.log('currentImageIndex:', currentImageIndex);
    }

    if (imageCounter) {
        imageCounter.textContent = `${currentImageIndex + 1} of ${currentGalleryImages.length}`;
        console.log('Updated counter to:', imageCounter.textContent);
    }
}

// Function to toggle Sugar Partner image zoom
function toggleSugarPartnerImageZoom(img) {
    if (img.style.transform === '' || img.style.transform === 'scale(1)') {
        img.style.transform = 'scale(2)';
        img.style.cursor = 'zoom-out';
        img.style.transition = 'transform 0.3s ease';
    } else {
        img.style.transform = 'scale(1)';
        img.style.cursor = 'zoom-in';
        img.style.transition = 'transform 0.3s ease';
    }
}

// Function to handle keyboard navigation in Sugar Partner lightbox (renamed to avoid conflicts)
function handleSugarPartnerLightboxKeydown(event) {
    // Only handle keys if Sugar Partner lightbox is open
    const lightbox = document.getElementById('image-lightbox');
    if (!lightbox) {
        console.log('Sugar Partner lightbox not found, ignoring keydown');
        return;
    }

    console.log('Keyboard event in Sugar Partner lightbox:', event.key);

    // Prevent default behavior for navigation keys
    if (event.key === 'ArrowLeft' || event.key === 'ArrowRight' || event.key === 'Escape') {
        event.preventDefault();
        event.stopPropagation();
    }

    switch(event.key) {
        case 'Escape':
            console.log('Escape key pressed - closing Sugar Partner lightbox');
            event.preventDefault();
            event.stopPropagation();
            closeSugarPartnerLightbox();
            break;
        case 'ArrowLeft':
            console.log('Left arrow key pressed in Sugar Partner lightbox');
            event.preventDefault();
            event.stopPropagation();
            previousSugarPartnerImage();
            break;
        case 'ArrowRight':
            console.log('Right arrow key pressed in Sugar Partner lightbox');
            event.preventDefault();
            event.stopPropagation();
            nextSugarPartnerImage();
            break;
    }
}

// Debug function to test Sugar Partner gallery functionality
function testSugarPartnerGallery() {
    console.log('=== Sugar Partner Gallery Debug Info ===');
    console.log('Current gallery images:', currentGalleryImages);
    console.log('Current image index:', currentImageIndex);
    console.log('Gallery grid element:', document.getElementById('gallery-grid'));
    console.log('Available functions:');
    console.log('- openImageLightbox:', typeof openImageLightbox);
    console.log('- showSugarPartnerLightbox:', typeof showSugarPartnerLightbox);
    console.log('- closeSugarPartnerLightbox:', typeof closeSugarPartnerLightbox);

    if (currentGalleryImages && currentGalleryImages.length > 0) {
        console.log('Testing Sugar Partner lightbox with first image...');
        openImageLightbox(0);
    } else {
        console.log('No gallery images available to test');
    }
}

// Make test function available globally for debugging
window.testSugarPartnerGallery = testSugarPartnerGallery;
window.closeSugarPartnerLightbox = closeSugarPartnerLightbox; // Make available globally for debugging

</script>
